<!-- Hero Section with Cover Image, Title and Status -->
<div class="bg-gradient-to-br from-purple-50/30 via-pink-50/20 to-red-50/30 dark:from-gray-900/50 dark:via-gray-800/30 dark:to-gray-800/50 rounded-lg p-6 transition-all duration-200">
  <div class="flex flex-col sm:flex-row items-center gap-6">
    <!-- Cover Image -->
    <div class="flex-shrink-0">
      <%= render "songs/song_cover", song: song %>
    </div>
    
    <!-- Title and Status Section -->
    <div class="flex-1 text-center sm:text-left">
      <%= render "songs/song_title_section", song: song, edit_mode: local_assigns.fetch(:edit_mode, false) %>
      <%= render "songs/song_status", song: song %>
      <%= render "songs/song_details", song: song %>
    </div>
  </div>
</div>

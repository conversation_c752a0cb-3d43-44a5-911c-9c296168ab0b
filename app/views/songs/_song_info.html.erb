<!-- Song Details Layout -->
<div class="flex flex-col gap-4 p-4">
  <!-- Hero Section -->
  <%= render "songs/song_hero_section", song: song, edit_mode: local_assigns.fetch(:edit_mode, false) %>
  <!-- Quick Actions -->
  <%= render "songs/song_quick_actions", song: song %>
  <!-- Metadata Information -->
  <%= render "songs/song_metadata_card", song: song %>
  <!-- Lyrics Section -->
  <%= render "songs/song_lyrics", song: song %>
  <!-- Additional Actions -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-purple-200/30 dark:border-gray-700 p-4 transition-all duration-200">
    <div class="flex flex-col sm:flex-row gap-3">
      <!-- Delete Button -->
      <%= button_to song,
              class: "flex-1 inline-flex justify-center items-center px-4 py-2.5 text-sm font-medium text-center text-white bg-red-600 rounded-lg hover:bg-red-700 focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 cursor-pointer transition-all duration-200",
              method: :delete,
              form: { data: { turbo_confirm: "Are you sure you want to delete '#{song.title}'? This action cannot be undone." } } do %>
        <%= flowbite_icon('trash-bin-outline', class: 'size-4 mr-2') %>
        Delete Song
      <% end %>
      <button type="button" disabled
                  class="flex-1 inline-flex justify-center items-center px-4 py-2.5 text-sm font-medium text-center text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 focus:ring-4 focus:outline-none focus:ring-gray-300 dark:focus:ring-gray-600 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-gray-100 dark:disabled:hover:bg-gray-600 transition-all duration-200">
        <%= flowbite_icon('share-nodes-solid', class: 'size-4 mr-2') %>
        Share (Coming Soon)
      </button>
    </div>
  </div>
</div>

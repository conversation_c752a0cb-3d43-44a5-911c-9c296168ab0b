<!-- Status with Icon (if not final audio) -->
<% unless song.has_final_audio? %>
  <div class="flex justify-center sm:justify-start mt-3">
    <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium shadow-sm <%=
          case song.generation_task.status
          when 'failed'
            'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
          when 'pending'
            'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
          else
            'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
          end %>">
      <%= case song.generation_task.status
              when 'failed'
                if song.generation_task.error_data&.dig('error') == '任务超时'
                  flowbite_icon('clock-outline', class: 'size-4 mr-2')
                else
                  flowbite_icon('exclamation-circle-outline', class: 'size-4 mr-2')
                end
              when 'pending'
                flowbite_icon('clock-outline', class: 'size-4 mr-2')
              else
                render "shared/loading_icon", class: 'size-4 mr-2 animate-spin'
              end %>
      <%= if song.generation_task.status == 'failed' && song.generation_task.error_data&.dig('error') == '任务超时'
            '任务超时'
          else
            song.generation_task.status.humanize
          end %>
    </div>
  </div>
<% end %>

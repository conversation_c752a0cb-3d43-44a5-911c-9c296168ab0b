<% content_for :title, @song.title %>
<div class="min-h-screen bg-gradient-to-br from-purple-50/30 via-pink-50/20 to-red-50/30 dark:from-gray-900/50 dark:via-gray-800/30 dark:to-gray-800/50 transition-all duration-200">
  <!-- Header Section -->
  <div class="border-b border-purple-200/30 dark:border-gray-700/50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-3xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 bg-clip-text text-transparent dark:from-purple-400 dark:via-pink-400 dark:to-red-400">Song Details</h1>
          <p class="text-gray-600 dark:text-gray-300 mt-1">View and manage your generated song</p>
        </div>
        <div class="flex space-x-3">
          <%= link_to songs_path,
              class: "text-gray-900 dark:text-white bg-white dark:bg-gray-800 border border-purple-200/30 dark:border-gray-700 focus:outline-none hover:bg-gray-100 dark:hover:bg-gray-700 focus:ring-4 focus:ring-purple-300 dark:focus:ring-purple-600 font-medium rounded-lg text-sm px-5 py-2.5 transition-all duration-200" do %>
            <%= flowbite_icon('arrow-left-solid', class: 'w-4 h-4 mr-2') %>
            Back to Songs
          <% end %>
        </div>
      </div>
    </div>
  </div>
  <!-- Main Content -->
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Song Card -->
    <div class="w-full bg-white dark:bg-gray-800 border border-purple-200/30 dark:border-gray-700 rounded-lg shadow-sm mx-auto transition-all duration-200 hover:shadow-md">
      <%= turbo_frame_tag "song_details" do %>
        <%= render "song_info", song: @song %>
      <% end %>
    </div>
  </div>
  <!-- Persistent/Floating Media Player -->
  <div class="fixed bottom-0 left-0 right-0 md:left-64 z-50 transition-all duration-300 ease-in-out">
    <!-- Main player container -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <!-- Player component -->
      <%= turbo_frame_tag "song_player", class: "mt-2" do %>
        <%= render "player", song: @song %>
      <% end %>
    </div>
  </div>
</div>
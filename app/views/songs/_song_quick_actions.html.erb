<!-- Quick Actions Card -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-purple-200/30 dark:border-gray-700 p-4 transition-all duration-200">
  <div class="flex flex-col sm:flex-row gap-3 justify-center sm:justify-start">
    <!-- Play Button -->
    <% if song.has_stream_audio? || song.has_final_audio? %>
      <%= link_to song_path(song), 
          data: { turbo_frame: "song_player" },
          class: "flex-1 sm:flex-none inline-flex justify-center items-center px-4 py-2.5 text-sm font-medium text-center text-white bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg hover:from-purple-700 hover:to-pink-700 focus:ring-4 focus:outline-none focus:ring-purple-300 dark:focus:ring-purple-800 transition-all duration-200 cursor-pointer" do %>
        <%= flowbite_icon('play-outline', class: 'size-4 mr-2') %>
        Play
      <% end %>
    <% else %>
      <button type="button" disabled
                  class="flex-1 sm:flex-none inline-flex justify-center items-center px-4 py-2.5 text-sm font-medium text-center text-gray-400 bg-gray-300 dark:bg-gray-600 rounded-lg cursor-not-allowed">
        <%= flowbite_icon('clock-outline', class: 'size-4 mr-2') %>
        Processing...
      </button>
    <% end %>

    <!-- Favorite Button -->
    <%= render "songs/favorite_button", song: song %>

    <!-- Download Button -->
    <% if song.has_final_audio? %>
      <a href="<%= song.audio_url %>" target="_blank" download="<%= download_filename_for_song(song) %>"
             class="flex-1 sm:flex-none inline-flex justify-center items-center px-4 py-2.5 text-sm font-medium text-center text-white bg-gradient-to-r from-green-600 to-emerald-600 rounded-lg hover:from-green-700 hover:to-emerald-700 focus:ring-4 focus:outline-none focus:ring-green-300 dark:focus:ring-green-800 transition-all duration-200">
        <%= flowbite_icon('download-outline', class: 'size-4 mr-2') %>
        Download
      </a>
    <% elsif song.has_stream_audio? %>
      <button type="button" disabled
                  class="flex-1 sm:flex-none inline-flex justify-center items-center px-4 py-2.5 text-sm font-medium text-center text-gray-400 bg-gray-300 dark:bg-gray-600 rounded-lg cursor-not-allowed">
        <%= render "shared/loading_icon", class: 'size-4 mr-2 animate-spin' %>
        Processing...
      </button>
    <% else %>
      <button type="button" disabled
                  class="flex-1 sm:flex-none inline-flex justify-center items-center px-4 py-2.5 text-sm font-medium text-center text-gray-400 bg-gray-300 dark:bg-gray-600 rounded-lg cursor-not-allowed">
        <%= flowbite_icon('clock-outline', class: 'size-4 mr-2') %>
        Generating...
      </button>
    <% end %>
  </div>
</div>

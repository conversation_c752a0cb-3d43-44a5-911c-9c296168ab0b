<% edit_mode = local_assigns.fetch(:edit_mode, false) %>
<div>
  <% if edit_mode %>
    <!-- Edit Form -->
    <%= form_with model: song, local: false, class: "space-y-3" do |form| %>
      <div>
        <%= form.text_field :title,
              value: song.title,
              id: "song_title",
              class: "bg-transparent border-0 border-b-2 border-purple-300 dark:border-purple-600 text-gray-900 dark:text-white text-xl font-bold text-center rounded-none focus:ring-0 focus:border-purple-500 dark:focus:border-purple-400 block w-full p-2 placeholder-gray-400 dark:placeholder-gray-500",
              placeholder: "Enter song title",
              maxlength: 100,
              required: true %>
        <% if song.errors[:title].any? %>
          <p class="mt-1 text-xs text-red-600"><%= song.errors[:title].first %></p>
        <% end %>
      </div>
      <div class="flex space-x-2 justify-center">
        <%= form.submit "Save",
              class: "text-white bg-purple-600 hover:bg-purple-700 focus:ring-2 focus:outline-none focus:ring-purple-300 font-medium rounded-md text-xs px-3 py-1.5 transition-colors" %>
        <%= link_to "Cancel",
              song_path(song),
              data: { turbo_frame: "song_details" },
              class: "text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white focus:outline-none bg-transparent hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md px-3 py-1.5 transition-colors" %>
      </div>
    <% end %>
  <% else %>
    <!-- Display Mode -->
    <div class="flex items-center justify-center sm:justify-start space-x-2 mb-2">
      <h2 class="text-xl sm:text-2xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 bg-clip-text text-transparent dark:from-purple-400 dark:via-pink-400 dark:to-red-400">
        <%= turbo_frame_tag dom_id(song, :info_title), class: "contents" do %>
          <%= song.title %>
        <% end %>
      </h2>
      <%= link_to edit_song_path(song),
            data: { turbo_frame: "song_details" },
            class: "inline-flex items-center p-1.5 text-sm font-medium text-center text-gray-400 dark:text-gray-500 rounded-md hover:bg-white/50 dark:hover:bg-gray-700/50 hover:text-purple-600 dark:hover:text-purple-400 focus:ring-2 focus:outline-none focus:ring-purple-300 dark:focus:ring-purple-600 transition-all",
            title: "Edit song title" do %>
        <%= flowbite_icon('edit-outline', class: 'size-4') %>
      <% end %>
    </div>
  <% end %>
</div>

<!-- <PERSON> Card -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-purple-200/30 dark:border-gray-700 p-4 transition-all duration-200">
  <h3 class="text-lg font-semibold bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 bg-clip-text text-transparent dark:from-purple-400 dark:via-pink-400 dark:to-red-400 mb-4">Song Information</h3>
  
  <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
    <!-- Created Date -->
    <div class="flex items-center space-x-3">
      <div class="flex-shrink-0">
        <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
          <%= flowbite_icon('calendar-outline', class: 'size-4 text-purple-600 dark:text-purple-400') %>
        </div>
      </div>
      <div class="flex-1 min-w-0">
        <p class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Created</p>
        <p class="text-sm text-gray-900 dark:text-white truncate"><%= song.created_at.strftime("%b %d, %Y") %></p>
      </div>
    </div>

    <!-- Duration -->
    <div class="flex items-center space-x-3">
      <div class="flex-shrink-0">
        <div class="w-8 h-8 bg-pink-100 dark:bg-pink-900/30 rounded-lg flex items-center justify-center">
          <%= flowbite_icon('clock-outline', class: 'size-4 text-pink-600 dark:text-pink-400') %>
        </div>
      </div>
      <div class="flex-1 min-w-0">
        <p class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Duration</p>
        <p class="text-sm text-gray-900 dark:text-white truncate">
          <%= song.duration_formatted || "Processing..." %>
        </p>
      </div>
    </div>

    <!-- Style -->
    <div class="flex items-center space-x-3">
      <div class="flex-shrink-0">
        <div class="w-8 h-8 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center">
          <%= flowbite_icon('music-outline', class: 'size-4 text-red-600 dark:text-red-400') %>
        </div>
      </div>
      <div class="flex-1 min-w-0">
        <p class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Style</p>
        <p class="text-sm text-gray-900 dark:text-white truncate">
          <%= song.generation_style || "Not specified" %>
        </p>
      </div>
    </div>
  </div>
</div>

<!-- Lyrics Card -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-purple-200/30 dark:border-gray-700 p-4 transition-all duration-200">
  <h3 class="text-lg font-semibold bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 bg-clip-text text-transparent dark:from-purple-400 dark:via-pink-400 dark:to-red-400 mb-4">Lyrics</h3>
  <% if song.has_lyrics? && song.lyrics_text.present? %>
    <div class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 rounded-lg p-4 max-h-64 overflow-y-auto">
      <div class="text-gray-700 dark:text-gray-300 text-sm whitespace-pre-line leading-relaxed">
        <%= song.lyrics_text %>
      </div>
    </div>
  <% elsif song.generation_task.instrumental? %>
    <div class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 rounded-lg p-4 text-center">
      <div class="text-gray-500 dark:text-gray-400 text-sm flex items-center justify-center">
        <%= flowbite_icon('music-outline', class: 'size-5 mr-2') %>
        This is an instrumental track
      </div>
    </div>
  <% elsif song.generation_task.pending? || song.generation_task.in_progress? %>
    <div class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 rounded-lg p-4 text-center">
      <div class="text-gray-500 dark:text-gray-400 text-sm flex items-center justify-center">
        <%= render "shared/loading_icon", class: 'size-5 mr-2 animate-spin' %>
        Lyrics being generated...
      </div>
    </div>
  <% else %>
    <div class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 rounded-lg p-4 text-center">
      <div class="text-gray-500 dark:text-gray-400 text-sm flex items-center justify-center">
        <%= flowbite_icon('file-text-outline', class: 'size-5 mr-2') %>
        No lyrics available
      </div>
    </div>
  <% end %>
</div>
